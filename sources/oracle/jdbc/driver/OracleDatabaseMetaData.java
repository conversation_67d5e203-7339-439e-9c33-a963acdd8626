package oracle.jdbc.driver;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.LinkedHashMap;
import java.util.Map;
import oracle.jdbc.OracleDatabaseMetaData;
import oracle.jdbc.internal.Monitor;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/OracleDatabaseMetaData.class */
class OracleDatabaseMetaData extends oracle.jdbc.OracleDatabaseMetaData {
    static final int RSFS = 4284;
    static final boolean DEBUG = false;
    private static final long FOUR_GIG_MINUS_ONE = 4294967295L;
    private long maxLogicalLobSize;

    public OracleDatabaseMetaData(oracle.jdbc.internal.OracleConnection conn) {
        super(conn);
        this.maxLogicalLobSize = -1L;
    }

    public OracleDatabaseMetaData(OracleConnection conn) {
        this((oracle.jdbc.internal.OracleConnection) conn);
    }

    @Override // oracle.jdbc.OracleDatabaseMetaData, java.sql.DatabaseMetaData
    public ResultSet getColumns(String catalog, String schemaPattern, String tableNamePattern, String columnNamePattern) throws SQLException {
        String plsql;
        String bindSchema;
        String bindTable;
        String bindColumn;
        Monitor.CloseableLock lock = this.connection.acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                boolean includeSynonyms = this.connection.getIncludeSynonyms();
                if (includeSynonyms && schemaPattern != null && !hasSqlWildcard(schemaPattern) && tableNamePattern != null && !hasSqlWildcard(tableNamePattern)) {
                    plsql = getColumnsNoWildcardsPlsql();
                    bindSchema = stripSqlEscapes(schemaPattern);
                    bindTable = stripSqlEscapes(tableNamePattern);
                    bindColumn = columnNamePattern == null ? "%" : columnNamePattern;
                } else {
                    plsql = getColumnsWithWildcardsPlsql(includeSynonyms);
                    bindSchema = schemaPattern == null ? "%" : schemaPattern;
                    bindTable = tableNamePattern == null ? "%" : tableNamePattern;
                    bindColumn = columnNamePattern == null ? "%" : columnNamePattern;
                }
                CallableStatement cstmt = this.connection.prepareCall(plsql);
                ((oracle.jdbc.internal.OracleStatement) cstmt.unwrap(oracle.jdbc.internal.OracleStatement.class)).setLongPrefetch(true);
                cstmt.setString(1, bindSchema);
                cstmt.setString(2, bindTable);
                cstmt.setString(3, bindColumn);
                cstmt.registerOutParameter(4, -10);
                cstmt.closeOnCompletion();
                cstmt.setPoolable(false);
                cstmt.execute();
                ResultSet rs = ((oracle.jdbc.OracleCallableStatement) cstmt).getCursor(4);
                if (rs.getFetchSize() < RSFS) {
                    rs.setFetchSize(RSFS);
                }
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                        }
                    } else {
                        lock.close();
                    }
                }
                return rs;
            } finally {
            }
        } catch (Throwable th3) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    String getColumnsNoWildcardsPlsql() throws SQLException {
        String queryPlsql;
        short db_version = this.connection.getVersionNumber();
        String queryPart2 = "       t.column_name AS column_name,\n" + datatypeQuery(OracleDatabaseMetaData.DataTypeSource.COLS, "t") + "       t.data_type AS type_name,\n       DECODE (t.data_precision,                null, DECODE(t.data_type,                        'NUMBER', DECODE(t.data_scale,                                    null, " + (((PhysicalConnection) this.connection).j2ee13Compliant ? "38" : "0") + "                                   , 38),          DECODE (t.data_type, 'CHAR', t.char_length,                   'VARCHAR', t.char_length,                   'VARCHAR2', t.char_length,                   'NVARCHAR2', t.char_length,                   'NCHAR', t.char_length,                   'NUMBER', 0,           t.data_length)                           ),         t.data_precision)\n              AS column_size,\n       0 AS buffer_length,\n       DECODE (t.data_type,                'NUMBER', DECODE(t.data_precision,                                 null, DECODE(t.data_scale,                                              null, " + (((PhysicalConnection) this.connection).j2ee13Compliant ? "0" : "-127") + "                                             , t.data_scale),                                  t.data_scale),                t.data_scale) AS decimal_digits,\n       10 AS num_prec_radix,\n       DECODE (t.nullable, 'N', 0, 1) AS nullable,\n";
        String queryPart4JDBC4 = "       null as SCOPE_CATALOG,\n       null as SCOPE_SCHEMA,\n       null as SCOPE_TABLE,\n       null as SOURCE_DATA_TYPE,\n" + (db_version >= 12000 ? "       t.identity_column as IS_AUTOINCREMENT,\n       t.virtual_column as IS_GENERATEDCOLUMN\n" : "       'NO' as IS_AUTOINCREMENT,\n       null as IS_GENERATEDCOLUMN\n");
        String fromClause = db_version >= 12000 ? "FROM all_tab_cols t" : "FROM all_tab_columns t";
        String whereClause = "WHERE t.owner = out_owner \n  AND t.table_name = out_name\n  AND t.column_name LIKE ? ESCAPE '/'\n" + (db_version >= 12000 ? "  AND t.user_generated = 'YES'\n" : "");
        String queryPlsql2 = ("open xxx for SELECT NULL AS table_cat,\n       in_owner AS table_schem,\n       in_name AS table_name,\n") + queryPart2;
        if (this.connection.getRemarksReporting()) {
            queryPlsql = queryPlsql2 + "       c.comments AS remarks,\n";
        } else {
            queryPlsql = queryPlsql2 + "       NULL AS remarks,\n";
        }
        String queryPlsql3 = queryPlsql + "       t.data_default AS column_def,\n       0 AS sql_data_type,\n       0 AS sql_datetime_sub,\n       t.data_length AS char_octet_length,\n       t.column_id AS ordinal_position,\n       DECODE (t.nullable, 'N', 'NO', 'YES') AS is_nullable,\n" + queryPart4JDBC4 + fromClause;
        if (this.connection.getRemarksReporting()) {
            queryPlsql3 = queryPlsql3 + ", all_col_comments c";
        }
        String queryPlsql4 = queryPlsql3 + "\n" + whereClause;
        if (this.connection.getRemarksReporting()) {
            queryPlsql4 = queryPlsql4 + "  AND t.owner = c.owner (+)\n  AND t.table_name = c.table_name (+)\n  AND t.column_name = c.column_name (+)\n";
        }
        String finalProc = "declare\n  in_owner varchar2(256) := null;\n  in_name varchar2(256) := null;\n  my_user_name varchar2(256) := null;\n  cnt number := 0;\n  out_owner varchar2(256) := null;\n  out_name  varchar2(256):= null;\n  xxx SYS_REFCURSOR;\nbegin\n  in_owner := ?;\n  in_name := ?;\n  select user into my_user_name from dual;\n  if (my_user_name = in_owner) then\n    select count(*) into cnt from user_tables\n      where table_name = in_name;\n    if (cnt = 1) then\n      out_owner := in_owner;\n      out_name := in_name;\n    else\n      select count(*) into cnt from user_views\n        where view_name = in_name;\n      if (cnt = 1) then\n        out_owner := in_owner;\n        out_name := in_name;\n      else\n        begin\n          select table_owner, table_name into out_owner, out_name\n            from all_synonyms\n            where CONNECT_BY_ISLEAF = 1\n            and db_link is NULL\n            start with owner = in_owner and synonym_name = in_name\n            connect by prior table_name = synonym_name\n                    and prior table_owner = owner;\n        exception\n          when NO_DATA_FOUND then\n            out_owner := null;\n            out_name := null;\n        end;\n      end if;\n    end if;\n  else\n    select count(*) into cnt from all_tables\n      where owner = in_owner and table_name = in_name;\n    if (cnt = 1) then\n      out_owner := in_owner;\n      out_name := in_name;\n    else\n      select count(*) into cnt from all_views\n         where owner = in_owner and view_name = in_name;\n      if (cnt = 1) then\n        out_owner := in_owner;\n        out_name := in_name;\n      else\n        begin\n          select table_owner, table_name into out_owner, out_name\n            from all_synonyms\n            where CONNECT_BY_ISLEAF = 1\n            and db_link is NULL\n            start with owner = in_owner and synonym_name = in_name\n            connect by prior table_name = synonym_name\n                    and prior table_owner = owner;\n        exception\n          when NO_DATA_FOUND then\n            out_owner := null;\n            out_name := null;\n        end;\n      end if;\n    end if;\n  end if;\n" + (queryPlsql4 + "\nORDER BY table_schem, table_name, ordinal_position\n") + "; \n ? := xxx;\n end;";
        return finalProc;
    }

    String getColumnsWithWildcardsPlsql(boolean includeSynonyms) throws SQLException {
        String finalQuery;
        String finalQuery2;
        short db_version = this.connection.getVersionNumber();
        String queryHint = "";
        if ((db_version >= 10200) & (db_version < 11100) & includeSynonyms) {
            queryHint = "/*+ CHOOSE */";
        }
        String queryPart2 = "       t.column_name AS column_name,\n" + datatypeQuery(OracleDatabaseMetaData.DataTypeSource.COLS, "t") + "       t.data_type AS type_name,\n       DECODE (t.data_precision,                null, DECODE(t.data_type,                        'NUMBER', DECODE(t.data_scale,                                    null, " + (((PhysicalConnection) this.connection).j2ee13Compliant ? "38" : "0") + "                                   , 38),        DECODE (t.data_type,                'CHAR', t.char_length,                'VARCHAR', t.char_length,                'VARCHAR2', t.char_length,                'NVARCHAR2', t.char_length,                'NCHAR', t.char_length,                'NUMBER', 0,                t.data_length)                           ),         t.data_precision)\n              AS column_size,\n       0 AS buffer_length,\n       DECODE (t.data_type,                'NUMBER', DECODE(t.data_precision,                                 null, DECODE(t.data_scale,                                              null, " + (((PhysicalConnection) this.connection).j2ee13Compliant ? "0" : "-127") + "                                             , t.data_scale),                                  t.data_scale),                t.data_scale) AS decimal_digits,\n       10 AS num_prec_radix,\n       DECODE (t.nullable, 'N', 0, 1) AS nullable,\n";
        String queryPart4JDBC4 = "       null as SCOPE_CATALOG,\n       null as SCOPE_SCHEMA,\n       null as SCOPE_TABLE,\n       null as SOURCE_DATA_TYPE,\n" + (db_version >= 12000 ? "       t.identity_column as IS_AUTOINCREMENT,\n       t.virtual_column as IS_GENERATEDCOLUMN\n" : "       'NO' as IS_AUTOINCREMENT,\n       null as IS_GENERATEDCOLUMN\n");
        String fromClause = db_version >= 12000 ? "FROM all_tab_cols t" : "FROM all_tab_columns t";
        String whereClause = "WHERE t.owner LIKE in_owner ESCAPE '/'\n  AND t.table_name LIKE in_name ESCAPE '/'\n  AND t.column_name LIKE in_column ESCAPE '/'\n" + (db_version >= 12000 ? "  AND t.user_generated = 'YES'\n" : "");
        String synonymWhereClause = "WHERE t.owner = s.table_owner\n  AND t.table_name = s.table_name\n  AND t.column_name LIKE in_column ESCAPE '/'\n" + (db_version >= 12000 ? "  AND t.user_generated = 'YES'\n" : "");
        String finalQuery3 = ("open xyzzy for\nSELECT " + queryHint + "NULL AS table_cat,\n       t.owner AS table_schem,\n       t.table_name AS table_name,\n") + queryPart2;
        if (this.connection.getRemarksReporting()) {
            finalQuery = finalQuery3 + "       c.comments AS remarks,\n";
        } else {
            finalQuery = finalQuery3 + "       NULL AS remarks,\n";
        }
        String finalQuery4 = finalQuery + "       t.data_default AS column_def,\n       0 AS sql_data_type,\n       0 AS sql_datetime_sub,\n       t.data_length AS char_octet_length,\n       t.column_id AS ordinal_position,\n       DECODE (t.nullable, 'N', 'NO', 'YES') AS is_nullable,\n" + queryPart4JDBC4 + fromClause;
        if (this.connection.getRemarksReporting()) {
            finalQuery4 = finalQuery4 + ", all_col_comments c";
        }
        String finalQuery5 = finalQuery4 + "\n" + whereClause;
        if (this.connection.getRemarksReporting()) {
            finalQuery5 = finalQuery5 + "  AND t.owner = c.owner (+)\n  AND t.table_name = c.table_name (+)\n  AND t.column_name = c.column_name (+)\n";
        }
        if (this.connection.getIncludeSynonyms()) {
            String finalQuery6 = ((finalQuery5 + "UNION ALL\n SELECT " + queryHint + "NULL AS table_cat,\n") + "       REGEXP_SUBSTR(LTRIM(s.owner, '/'), '[^/]+') AS table_schem,\n       REGEXP_SUBSTR(LTRIM(s.synonym_name, '/'),\n                           '[^/]+') AS table_name,\n") + queryPart2;
            if (this.connection.getRemarksReporting()) {
                finalQuery2 = finalQuery6 + "       c.comments AS remarks,\n";
            } else {
                finalQuery2 = finalQuery6 + "       NULL AS remarks,\n";
            }
            String finalQuery7 = (finalQuery2 + "       t.data_default AS column_def,\n       0 AS sql_data_type,\n       0 AS sql_datetime_sub,\n       t.data_length AS char_octet_length,\n       t.column_id AS ordinal_position,\n       DECODE (t.nullable, 'N', 'NO', 'YES') AS is_nullable,\n" + queryPart4JDBC4 + fromClause) + ", (SELECT SYS_CONNECT_BY_PATH(owner, '/') owner,\n          SYS_CONNECT_BY_PATH(synonym_name, '/')\n                              synonym_name,\n          table_owner, table_name\n  FROM all_synonyms\n   WHERE CONNECT_BY_ISLEAF = 1\n    AND db_link is NULL\n  START WITH owner LIKE in_owner ESCAPE '/'\n    AND synonym_name LIKE in_name ESCAPE '/'\n  CONNECT BY PRIOR table_name = synonym_name\n    AND PRIOR table_owner = owner) s";
            if (this.connection.getRemarksReporting()) {
                finalQuery7 = finalQuery7 + ", all_col_comments c";
            }
            finalQuery5 = finalQuery7 + "\n" + synonymWhereClause;
            if (this.connection.getRemarksReporting()) {
                finalQuery5 = finalQuery5 + "  AND t.owner = c.owner (+)\n  AND t.table_name = c.table_name (+)\n  AND t.column_name = c.column_name (+)\n";
            }
        }
        String finalProc = "declare\n  in_owner varchar2(256) := null;\n  in_name varchar2(256) := null;\n  in_column varchar2(256) := null;\n  xyzzy SYS_REFCURSOR;\nbegin\n  in_owner := ?;\n  in_name := ?;\n  in_column := ?;\n" + (finalQuery5 + "ORDER BY table_schem, table_name, ordinal_position\n") + "; \n ? := xyzzy;\n end;";
        return finalProc;
    }

    @Override // oracle.jdbc.OracleDatabaseMetaData, java.sql.DatabaseMetaData
    public ResultSet getTypeInfo() throws SQLException {
        Monitor.CloseableLock lock = this.connection.acquireCloseableLock();
        Throwable th = null;
        try {
            Statement s = this.connection.createStatement();
            this.connection.getVersionNumber();
            int varTypeMaxLenCompat = this.connection.getVarTypeMaxLenCompat();
            boolean isExtended = varTypeMaxLenCompat == 2;
            if (isExtended) {
                try {
                    CallableStatement cstmt = this.connection.prepareCall("begin ? := private_jdbc.get_max_string_size(); end;");
                    Throwable th2 = null;
                    try {
                        try {
                            cstmt.registerOutParameter(1, 12);
                            cstmt.execute();
                            isExtended = "EXTENDED".equalsIgnoreCase(cstmt.getString(1));
                            if (cstmt != null) {
                                if (0 != 0) {
                                    try {
                                        cstmt.close();
                                    } catch (Throwable th3) {
                                        th2.addSuppressed(th3);
                                    }
                                } else {
                                    cstmt.close();
                                }
                            }
                        } finally {
                        }
                    } catch (Throwable th4) {
                        if (cstmt != null) {
                            if (th2 != null) {
                                try {
                                    cstmt.close();
                                } catch (Throwable th5) {
                                    th2.addSuppressed(th5);
                                }
                            } else {
                                cstmt.close();
                            }
                        }
                        throw th4;
                    }
                } catch (SQLException ex) {
                    if (ex.getErrorCode() != 6550) {
                        throw ex;
                    }
                }
            }
            int charPrecision = 2000;
            int ncharPrecision = 2000;
            int varcharPrecision = 4000;
            int nvarcharPrecision = 4000;
            int rawPrecision = 2000;
            if (isExtended) {
                charPrecision = 2000;
                ncharPrecision = 2000;
                varcharPrecision = 32767;
                nvarcharPrecision = 32766;
                rawPrecision = 32767;
            }
            String char_query = "union select\n 'CHAR' as type_name, 1 as data_type, " + charPrecision + " as precision,\n '''' as literal_prefix, '''' as literal_suffix, NULL as create_params,\n 1 as nullable, 1 as case_sensitive, 3 as searchable,\n 0 as unsigned_attribute, 0 as fixed_prec_scale, 0 as auto_increment,\n 'CHAR' as local_type_name, 0 as minimum_scale, 0 as maximum_scale,\n NULL as sql_data_type, NULL as sql_datetime_sub, 10 as num_prec_radix\nfrom dual\n";
            String nchar_query = "union select\n 'NCHAR' as type_name, -15 as data_type, " + ncharPrecision + " as precision,\n '''' as literal_prefix, '''' as literal_suffix, NULL as create_params,\n 1 as nullable, 1 as case_sensitive, 3 as searchable,\n 0 as unsigned_attribute, 0 as fixed_prec_scale, 0 as auto_increment,\n 'NCHAR' as local_type_name, 0 as minimum_scale, 0 as maximum_scale,\n NULL as sql_data_type, NULL as sql_datetime_sub, 10 as num_prec_radix\nfrom dual\n";
            String varchar2_query = "union select\n 'VARCHAR2' as type_name, 12 as data_type, " + varcharPrecision + " as precision,\n '''' as literal_prefix, '''' as literal_suffix, NULL as create_params,\n 1 as nullable, 1 as case_sensitive, 3 as searchable,\n 0 as unsigned_attribute, 0 as fixed_prec_scale, 0 as auto_increment,\n 'VARCHAR2' as local_type_name, 0 as minimum_scale, 0 as maximum_scale,\n NULL as sql_data_type, NULL as sql_datetime_sub, 10 as num_prec_radix\nfrom dual\n";
            String nvarchar2_query = "union select\n 'NVARCHAR2' as type_name, -9 as data_type, " + nvarcharPrecision + " as precision,\n '''' as literal_prefix, '''' as literal_suffix, NULL as create_params,\n 1 as nullable, 1 as case_sensitive, 3 as searchable,\n 0 as unsigned_attribute, 0 as fixed_prec_scale, 0 as auto_increment,\n 'nVARCHAR2' as local_type_name, 0 as minimum_scale, 0 as maximum_scale,\n NULL as sql_data_type, NULL as sql_datetime_sub, 10 as num_prec_radix\nfrom dual\n";
            String date_query = "union select\n 'DATE' as type_name, " + (((PhysicalConnection) this.connection).mapDateToTimestamp ? "93" : "91") + "as data_type, 7 as precision,\n 'DATE ''' as literal_prefix, '''' as literal_suffix, NULL as create_params,\n 1 as nullable, 0 as case_sensitive, 3 as searchable,\n 0 as unsigned_attribute, 0 as fixed_prec_scale, 0 as auto_increment,\n 'DATE' as local_type_name, 0 as minimum_scale, 0 as maximum_scale,\n NULL as sql_data_type, NULL as sql_datetime_sub, 10 as num_prec_radix\nfrom dual\n";
            String raw_query = "union select\n 'RAW' as type_name, -3 as data_type, " + rawPrecision + " as precision,\n '''' as literal_prefix, '''' as literal_suffix, NULL as create_params,\n 1 as nullable, 0 as case_sensitive, 3 as searchable,\n 0 as unsigned_attribute, 0 as fixed_prec_scale, 0 as auto_increment,\n 'RAW' as local_type_name, 0 as minimum_scale, 0 as maximum_scale,\n NULL as sql_data_type, NULL as sql_datetime_sub, 10 as num_prec_radix\nfrom dual\n";
            String blob_query = "union select\n 'BLOB' as type_name, 2004 as data_type, -1 as precision,\n null as literal_prefix, null as literal_suffix, NULL as create_params,\n 1 as nullable, 0 as case_sensitive, 0 as searchable,\n 0 as unsigned_attribute, 0 as fixed_prec_scale, 0 as auto_increment,\n 'BLOB' as local_type_name, 0 as minimum_scale, 0 as maximum_scale,\n NULL as sql_data_type, NULL as sql_datetime_sub, 10 as num_prec_radix\nfrom dual\n";
            String clob_query = "union select\n 'CLOB' as type_name, 2005 as data_type, -1 as precision,\n '''' as literal_prefix, '''' as literal_suffix, NULL as create_params,\n 1 as nullable, 1 as case_sensitive, 0 as searchable,\n 0 as unsigned_attribute, 0 as fixed_prec_scale, 0 as auto_increment,\n 'CLOB' as local_type_name, 0 as minimum_scale, 0 as maximum_scale,\n NULL as sql_data_type, NULL as sql_datetime_sub, 10 as num_prec_radix\nfrom dual\n";
            String nclob_query = "union select\n 'NCLOB' as type_name, 2011 as data_type, -1 as precision,\n '''' as literal_prefix, '''' as literal_suffix, NULL as create_params,\n 1 as nullable, 1 as case_sensitive, 0 as searchable,\n 0 as unsigned_attribute, 0 as fixed_prec_scale, 0 as auto_increment,\n 'NCLOB' as local_type_name, 0 as minimum_scale, 0 as maximum_scale,\n NULL as sql_data_type, NULL as sql_datetime_sub, 10 as num_prec_radix\nfrom dual\n";
            String vector_query = (this.connection.getMajorVersionNumber() > 23 || (this.connection.getMajorVersionNumber() == 23 && this.connection.getMinorVersionNumber() >= 4)) ? "union select\n 'VECTOR' as type_name, -105 as data_type, 0 as precision,\n '''' as literal_prefix, '''' as literal_suffix, NULL as create_params,\n 1 as nullable, 0 as case_sensitive, 0 as searchable,\n 0 as unsigned_attribute, 0 as fixed_prec_scale, 0 as auto_increment,\n 'VECTOR' as local_type_name, 0 as minimum_scale, 0 as maximum_scale,\n NULL as sql_data_type, NULL as sql_datetime_sub, NULL as num_prec_radix\nfrom dual\n" : "";
            String boolean_query = (this.connection.getMajorVersionNumber() > 23 || (this.connection.getMajorVersionNumber() == 23 && this.connection.getMinorVersionNumber() >= 1)) ? "union select\n 'BOOLEAN' as type_name, 16 as data_type, 0 as precision,\n NULL as literal_prefix, NULL as literal_suffix, NULL as create_params,\n 1 as nullable, 0 as case_sensitive, 3 as searchable,\n 0 as unsigned_attribute, 1 as fixed_prec_scale, 0 as auto_increment,\n 'BOOLEAN' as local_type_name, 0 as minimum_scale, 0 as maximum_scale,\n NULL as sql_data_type, NULL as sql_datetime_sub, 10 as num_prec_radix\n from dual\n" : "";
            String json_query = this.connection.getMajorVersionNumber() >= 21 ? "union select\n 'JSON' as type_name, 2016 as data_type, -1 as precision,\nNULL as literal_prefix, NULL as literal_suffix, NULL as create_params,\n1 as nullable, 0 as case_sensitive, 0 as searchable,\n0 as unsigned_attribute, 0 as fixed_prec_scale, 0 as auto_increment,\n'JSON' as local_type_name, 0 as minimum_scale, 0 as maximum_scale,\nNULL as sql_data_type, NULL as sql_datetime_sub, 10 as num_prec_radix\nfrom dual\n" : "";
            String query = "select\n 'NUMBER' as type_name, 2 as data_type, 38 as precision,\n NULL as literal_prefix, NULL as literal_suffix, NULL as create_params,\n 1 as nullable, 0 as case_sensitive, 3 as searchable,\n 0 as unsigned_attribute, 1 as fixed_prec_scale, 0 as auto_increment,\n 'NUMBER' as local_type_name, -84 as minimum_scale, 127 as maximum_scale,\n NULL as sql_data_type, NULL as sql_datetime_sub, 10 as num_prec_radix\nfrom dual\n" + char_query + nchar_query + varchar2_query + nvarchar2_query + date_query + "union select\n 'DATE' as type_name, 92 as data_type, 7 as precision,\n 'DATE ''' as literal_prefix, '''' as literal_suffix, NULL as create_params,\n 1 as nullable, 0 as case_sensitive, 3 as searchable,\n 0 as unsigned_attribute, 0 as fixed_prec_scale, 0 as auto_increment,\n 'DATE' as local_type_name, 0 as minimum_scale, 0 as maximum_scale,\n NULL as sql_data_type, NULL as sql_datetime_sub, 10 as num_prec_radix\nfrom dual\nunion select\n 'TIMESTAMP' as type_name, 93 as data_type, 11 as precision,\n 'TIMESTAMP ''' as literal_prefix, '''' as literal_suffix, NULL as create_params,\n 1 as nullable, 0 as case_sensitive, 3 as searchable,\n 0 as unsigned_attribute, 0 as fixed_prec_scale, 0 as auto_increment,\n 'TIMESTAMP' as local_type_name, 0 as minimum_scale, 0 as maximum_scale,\n NULL as sql_data_type, NULL as sql_datetime_sub, 10 as num_prec_radix\nfrom dual\nunion select\n 'TIMESTAMP WITH TIME ZONE' as type_name, -101 as data_type, 13 as precision,\n 'TIMESTAMP ''' as literal_prefix, '''' as literal_suffix, NULL as create_params,\n 1 as nullable, 0 as case_sensitive, 3 as searchable,\n 0 as unsigned_attribute, 0 as fixed_prec_scale, 0 as auto_increment,\n 'TIMESTAMP WITH TIME ZONE' as local_type_name, 0 as minimum_scale, 0 as maximum_scale,\n NULL as sql_data_type, NULL as sql_datetime_sub, 10 as num_prec_radix\nfrom dual\nunion select\n 'TIMESTAMP WITH LOCAL TIME ZONE' as type_name, -102 as data_type, 11 as precision,\n 'TIMESTAMP ''' as literal_prefix, '''' as literal_suffix, NULL as create_params,\n 1 as nullable, 0 as case_sensitive, 3 as searchable,\n 0 as unsigned_attribute, 0 as fixed_prec_scale, 0 as auto_increment,\n 'TIMESTAMP WITH LOCAL TIME ZONE' as local_type_name, 0 as minimum_scale, 0 as maximum_scale,\n NULL as sql_data_type, NULL as sql_datetime_sub, 10 as num_prec_radix\nfrom dual\nunion select\n 'INTERVALYM' as type_name, -103 as data_type, 5 as precision,\n 'INTERVAL ''' as literal_prefix, '''' as literal_suffix, NULL as create_params,\n 1 as nullable, 0 as case_sensitive, 3 as searchable,\n 0 as unsigned_attribute, 0 as fixed_prec_scale, 0 as auto_increment,\n 'INTERVALYM' as local_type_name, 0 as minimum_scale, 0 as maximum_scale,\n NULL as sql_data_type, NULL as sql_datetime_sub, 10 as num_prec_radix\nfrom dual\nunion select\n 'INTERVALDS' as type_name, -104 as data_type, 4 as precision,\n 'INTERVAL ''' as literal_prefix, '''' as literal_suffix, NULL as create_params,\n 1 as nullable, 0 as case_sensitive, 3 as searchable,\n 0 as unsigned_attribute, 0 as fixed_prec_scale, 0 as auto_increment,\n 'INTERVALDS' as local_type_name, 0 as minimum_scale, 0 as maximum_scale,\n NULL as sql_data_type, NULL as sql_datetime_sub, 10 as num_prec_radix\nfrom dual\n" + raw_query + "union select\n 'LONG' as type_name, -1 as data_type, 2147483647 as precision,\n '''' as literal_prefix, '''' as literal_suffix, NULL as create_params,\n 1 as nullable, 1 as case_sensitive, 0 as searchable,\n 0 as unsigned_attribute, 0 as fixed_prec_scale, 0 as auto_increment,\n 'LONG' as local_type_name, 0 as minimum_scale, 0 as maximum_scale,\n NULL as sql_data_type, NULL as sql_datetime_sub, 10 as num_prec_radix\nfrom dual\nunion select\n 'LONG RAW' as type_name, -4 as data_type, 2147483647 as precision,\n '''' as literal_prefix, '''' as literal_suffix, NULL as create_params,\n 1 as nullable, 0 as case_sensitive, 0 as searchable,\n 0 as unsigned_attribute, 0 as fixed_prec_scale, 0 as auto_increment,\n 'LONG RAW' as local_type_name, 0 as minimum_scale, 0 as maximum_scale,\n NULL as sql_data_type, NULL as sql_datetime_sub, 10 as num_prec_radix\nfrom dual\nunion select 'NUMBER' as type_name, -7 as data_type, 1 as precision,\nNULL as literal_prefix, NULL as literal_suffix, \n'(1)' as create_params, 1 as nullable, 0 as case_sensitive, 3 as searchable,\n0 as unsigned_attribute, 1 as fixed_prec_scale, 0 as auto_increment,\n'NUMBER' as local_type_name, -84 as minimum_scale, 127 as maximum_scale,\nNULL as sql_data_type, NULL as sql_datetime_sub, 10 as num_prec_radix\nfrom dual\nunion select 'NUMBER' as type_name, -6 as data_type, 3 as precision,\nNULL as literal_prefix, NULL as literal_suffix, \n'(3)' as create_params, 1 as nullable, 0 as case_sensitive, 3 as searchable,\n0 as unsigned_attribute, 1 as fixed_prec_scale, 0 as auto_increment,\n'NUMBER' as local_type_name, -84 as minimum_scale, 127 as maximum_scale,\nNULL as sql_data_type, NULL as sql_datetime_sub, 10 as num_prec_radix\nfrom dual\nunion select 'NUMBER' as type_name, 5 as data_type, 5 as precision,\nNULL as literal_prefix, NULL as literal_suffix, \n'(5)' as create_params, 1 as nullable, 0 as case_sensitive, 3 as searchable,\n0 as unsigned_attribute, 1 as fixed_prec_scale, 0 as auto_increment,\n'NUMBER' as local_type_name, -84 as minimum_scale, 127 as maximum_scale,\nNULL as sql_data_type, NULL as sql_datetime_sub, 10 as num_prec_radix\nfrom dual\nunion select 'NUMBER' as type_name, 4 as data_type, 10 as precision,\nNULL as literal_prefix, NULL as literal_suffix, \n'(10)' as create_params, 1 as nullable, 0 as case_sensitive, 3 as searchable,\n0 as unsigned_attribute, 1 as fixed_prec_scale, 0 as auto_increment,\n'NUMBER' as local_type_name, -84 as minimum_scale, 127 as maximum_scale,\nNULL as sql_data_type, NULL as sql_datetime_sub, 10 as num_prec_radix\nfrom dual\nunion select 'NUMBER' as type_name, -5 as data_type, 38 as precision,\nNULL as literal_prefix, NULL as literal_suffix, \nNULL as create_params, 1 as nullable, 0 as case_sensitive, 3 as searchable,\n0 as unsigned_attribute, 1 as fixed_prec_scale, 0 as auto_increment,\n'NUMBER' as local_type_name, -84 as minimum_scale, 127 as maximum_scale,\nNULL as sql_data_type, NULL as sql_datetime_sub, 10 as num_prec_radix\nfrom dual\nunion select 'FLOAT' as type_name, 6 as data_type, 63 as precision,\nNULL as literal_prefix, NULL as literal_suffix, \nNULL as create_params, 1 as nullable, 0 as case_sensitive, 3 as searchable,\n0 as unsigned_attribute, 1 as fixed_prec_scale, 0 as auto_increment,\n'FLOAT' as local_type_name, -84 as minimum_scale, 127 as maximum_scale,\nNULL as sql_data_type, NULL as sql_datetime_sub, 10 as num_prec_radix\nfrom dual\nunion select 'REAL' as type_name, 7 as data_type, 63 as precision,\nNULL as literal_prefix, NULL as literal_suffix, \nNULL as create_params, 1 as nullable, 0 as case_sensitive, 3 as searchable,\n0 as unsigned_attribute, 1 as fixed_prec_scale, 0 as auto_increment,\n'REAL' as local_type_name, -84 as minimum_scale, 127 as maximum_scale,\nNULL as sql_data_type, NULL as sql_datetime_sub, 10 as num_prec_radix\nfrom dual\n" + blob_query + clob_query + nclob_query + "union select\n 'REF' as type_name, 2006 as data_type, 0 as precision,\n '''' as literal_prefix, '''' as literal_suffix, NULL as create_params,\n 1 as nullable, 1 as case_sensitive, 0 as searchable,\n 0 as unsigned_attribute, 0 as fixed_prec_scale, 0 as auto_increment,\n 'REF' as local_type_name, 0 as minimum_scale, 0 as maximum_scale,\n NULL as sql_data_type, NULL as sql_datetime_sub, 10 as num_prec_radix\nfrom dual\nunion select\n 'ARRAY' as type_name, 2003 as data_type, 0 as precision,\n '''' as literal_prefix, '''' as literal_suffix, NULL as create_params,\n 1 as nullable, 1 as case_sensitive, 0 as searchable,\n 0 as unsigned_attribute, 0 as fixed_prec_scale, 0 as auto_increment,\n 'ARRAY' as local_type_name, 0 as minimum_scale, 0 as maximum_scale,\n NULL as sql_data_type, NULL as sql_datetime_sub, 10 as num_prec_radix\nfrom dual\nunion select\n 'STRUCT' as type_name, 2002 as data_type, 0 as precision,\n '''' as literal_prefix, '''' as literal_suffix, NULL as create_params,\n 1 as nullable, 1 as case_sensitive, 0 as searchable,\n 0 as unsigned_attribute, 0 as fixed_prec_scale, 0 as auto_increment,\n 'STRUCT' as local_type_name, 0 as minimum_scale, 0 as maximum_scale,\n NULL as sql_data_type, NULL as sql_datetime_sub, 10 as num_prec_radix\nfrom dual\n" + boolean_query + vector_query + json_query + "order by data_type\n";
            s.closeOnCompletion();
            oracle.jdbc.internal.OracleResultSet rs = (oracle.jdbc.internal.OracleResultSet) s.executeQuery(query);
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th6) {
                        th.addSuppressed(th6);
                    }
                } else {
                    lock.close();
                }
            }
            return rs;
        } catch (Throwable th7) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th8) {
                        th.addSuppressed(th8);
                    }
                } else {
                    lock.close();
                }
            }
            throw th7;
        }
    }

    @Override // oracle.jdbc.OracleDatabaseMetaData, oracle.jdbc.AdditionalDatabaseMetaData
    public String getAuditBanner() throws SQLException {
        Monitor.CloseableLock lock = this.connection.acquireCloseableLock();
        Throwable th = null;
        try {
            String auditBanner = ((PhysicalConnection) this.connection).getAuditBanner();
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                    }
                } else {
                    lock.close();
                }
            }
            return auditBanner;
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.jdbc.OracleDatabaseMetaData, oracle.jdbc.AdditionalDatabaseMetaData
    public String getAccessBanner() throws SQLException {
        Monitor.CloseableLock lock = this.connection.acquireCloseableLock();
        Throwable th = null;
        try {
            String accessBanner = ((PhysicalConnection) this.connection).getAccessBanner();
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                    }
                } else {
                    lock.close();
                }
            }
            return accessBanner;
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.jdbc.OracleDatabaseMetaData, oracle.jdbc.internal.AdditionalDatabaseMetaData
    public boolean isServerBigSCN() throws SQLException {
        return ((PhysicalConnection) this.connection).isServerBigSCN();
    }

    @Override // oracle.jdbc.OracleDatabaseMetaData
    public boolean isCompatible122OrGreater() throws SQLException {
        return ((PhysicalConnection) this.connection).isCompatible122OrGreater();
    }

    /* JADX WARN: Failed to apply debug info
    java.lang.NullPointerException: Cannot invoke "jadx.core.dex.instructions.args.InsnArg.getType()" because "changeArg" is null
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.moveListener(TypeUpdate.java:439)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.runListeners(TypeUpdate.java:232)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.requestUpdate(TypeUpdate.java:212)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.updateTypeForSsaVar(TypeUpdate.java:183)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.updateTypeChecked(TypeUpdate.java:112)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.apply(TypeUpdate.java:83)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.applyWithWiderIgnoreUnknown(TypeUpdate.java:74)
    	at jadx.core.dex.visitors.debuginfo.DebugInfoApplyVisitor.applyDebugInfo(DebugInfoApplyVisitor.java:137)
    	at jadx.core.dex.visitors.debuginfo.DebugInfoApplyVisitor.applyDebugInfo(DebugInfoApplyVisitor.java:133)
    	at jadx.core.dex.visitors.debuginfo.DebugInfoApplyVisitor.searchAndApplyVarDebugInfo(DebugInfoApplyVisitor.java:75)
    	at jadx.core.dex.visitors.debuginfo.DebugInfoApplyVisitor.lambda$applyDebugInfo$0(DebugInfoApplyVisitor.java:68)
    	at java.base/java.util.ArrayList.forEach(ArrayList.java:1604)
    	at jadx.core.dex.visitors.debuginfo.DebugInfoApplyVisitor.applyDebugInfo(DebugInfoApplyVisitor.java:68)
    	at jadx.core.dex.visitors.debuginfo.DebugInfoApplyVisitor.visit(DebugInfoApplyVisitor.java:55)
     */
    /* JADX WARN: Failed to calculate best type for var: r10v0 ??
    java.lang.NullPointerException: Cannot invoke "jadx.core.dex.instructions.args.InsnArg.getType()" because "changeArg" is null
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.moveListener(TypeUpdate.java:439)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.runListeners(TypeUpdate.java:232)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.requestUpdate(TypeUpdate.java:212)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.updateTypeForSsaVar(TypeUpdate.java:183)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.updateTypeChecked(TypeUpdate.java:112)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.apply(TypeUpdate.java:83)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.apply(TypeUpdate.java:56)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.calculateFromBounds(FixTypesVisitor.java:156)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.setBestType(FixTypesVisitor.java:133)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.deduceType(FixTypesVisitor.java:238)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.tryDeduceTypes(FixTypesVisitor.java:221)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.visit(FixTypesVisitor.java:91)
     */
    /* JADX WARN: Failed to calculate best type for var: r10v0 ??
    java.lang.NullPointerException: Cannot invoke "jadx.core.dex.instructions.args.InsnArg.getType()" because "changeArg" is null
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.moveListener(TypeUpdate.java:439)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.runListeners(TypeUpdate.java:232)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.requestUpdate(TypeUpdate.java:212)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.updateTypeForSsaVar(TypeUpdate.java:183)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.updateTypeChecked(TypeUpdate.java:112)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.apply(TypeUpdate.java:83)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.apply(TypeUpdate.java:56)
    	at jadx.core.dex.visitors.typeinference.TypeInferenceVisitor.calculateFromBounds(TypeInferenceVisitor.java:145)
    	at jadx.core.dex.visitors.typeinference.TypeInferenceVisitor.setBestType(TypeInferenceVisitor.java:123)
    	at jadx.core.dex.visitors.typeinference.TypeInferenceVisitor.lambda$runTypePropagation$2(TypeInferenceVisitor.java:101)
    	at java.base/java.util.ArrayList.forEach(ArrayList.java:1604)
    	at jadx.core.dex.visitors.typeinference.TypeInferenceVisitor.runTypePropagation(TypeInferenceVisitor.java:101)
    	at jadx.core.dex.visitors.typeinference.TypeInferenceVisitor.visit(TypeInferenceVisitor.java:75)
     */
    /* JADX WARN: Failed to calculate best type for var: r11v0 ??
    java.lang.NullPointerException: Cannot invoke "jadx.core.dex.instructions.args.InsnArg.getType()" because "changeArg" is null
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.moveListener(TypeUpdate.java:439)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.runListeners(TypeUpdate.java:232)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.requestUpdate(TypeUpdate.java:212)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.updateTypeForSsaVar(TypeUpdate.java:183)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.updateTypeChecked(TypeUpdate.java:112)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.apply(TypeUpdate.java:83)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.apply(TypeUpdate.java:56)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.calculateFromBounds(FixTypesVisitor.java:156)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.setBestType(FixTypesVisitor.java:133)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.deduceType(FixTypesVisitor.java:238)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.tryDeduceTypes(FixTypesVisitor.java:221)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.visit(FixTypesVisitor.java:91)
     */
    /* JADX WARN: Failed to calculate best type for var: r11v0 ??
    java.lang.NullPointerException: Cannot invoke "jadx.core.dex.instructions.args.InsnArg.getType()" because "changeArg" is null
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.moveListener(TypeUpdate.java:439)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.runListeners(TypeUpdate.java:232)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.requestUpdate(TypeUpdate.java:212)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.updateTypeForSsaVar(TypeUpdate.java:183)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.updateTypeChecked(TypeUpdate.java:112)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.apply(TypeUpdate.java:83)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.apply(TypeUpdate.java:56)
    	at jadx.core.dex.visitors.typeinference.TypeInferenceVisitor.calculateFromBounds(TypeInferenceVisitor.java:145)
    	at jadx.core.dex.visitors.typeinference.TypeInferenceVisitor.setBestType(TypeInferenceVisitor.java:123)
    	at jadx.core.dex.visitors.typeinference.TypeInferenceVisitor.lambda$runTypePropagation$2(TypeInferenceVisitor.java:101)
    	at java.base/java.util.ArrayList.forEach(ArrayList.java:1604)
    	at jadx.core.dex.visitors.typeinference.TypeInferenceVisitor.runTypePropagation(TypeInferenceVisitor.java:101)
    	at jadx.core.dex.visitors.typeinference.TypeInferenceVisitor.visit(TypeInferenceVisitor.java:75)
     */
    /* JADX WARN: Failed to calculate best type for var: r12v0 ??
    java.lang.NullPointerException: Cannot invoke "jadx.core.dex.instructions.args.InsnArg.getType()" because "changeArg" is null
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.moveListener(TypeUpdate.java:439)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.runListeners(TypeUpdate.java:232)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.requestUpdate(TypeUpdate.java:212)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.updateTypeForSsaVar(TypeUpdate.java:183)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.updateTypeChecked(TypeUpdate.java:112)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.apply(TypeUpdate.java:83)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.apply(TypeUpdate.java:56)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.calculateFromBounds(FixTypesVisitor.java:156)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.setBestType(FixTypesVisitor.java:133)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.deduceType(FixTypesVisitor.java:238)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.tryDeduceTypes(FixTypesVisitor.java:221)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.visit(FixTypesVisitor.java:91)
     */
    /* JADX WARN: Failed to calculate best type for var: r12v0 ??
    java.lang.NullPointerException: Cannot invoke "jadx.core.dex.instructions.args.InsnArg.getType()" because "changeArg" is null
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.moveListener(TypeUpdate.java:439)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.runListeners(TypeUpdate.java:232)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.requestUpdate(TypeUpdate.java:212)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.updateTypeForSsaVar(TypeUpdate.java:183)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.updateTypeChecked(TypeUpdate.java:112)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.apply(TypeUpdate.java:83)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.apply(TypeUpdate.java:56)
    	at jadx.core.dex.visitors.typeinference.TypeInferenceVisitor.calculateFromBounds(TypeInferenceVisitor.java:145)
    	at jadx.core.dex.visitors.typeinference.TypeInferenceVisitor.setBestType(TypeInferenceVisitor.java:123)
    	at jadx.core.dex.visitors.typeinference.TypeInferenceVisitor.lambda$runTypePropagation$2(TypeInferenceVisitor.java:101)
    	at java.base/java.util.ArrayList.forEach(ArrayList.java:1604)
    	at jadx.core.dex.visitors.typeinference.TypeInferenceVisitor.runTypePropagation(TypeInferenceVisitor.java:101)
    	at jadx.core.dex.visitors.typeinference.TypeInferenceVisitor.visit(TypeInferenceVisitor.java:75)
     */
    /* JADX WARN: Failed to calculate best type for var: r9v2 ??
    java.lang.NullPointerException: Cannot invoke "jadx.core.dex.instructions.args.InsnArg.getType()" because "changeArg" is null
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.moveListener(TypeUpdate.java:439)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.runListeners(TypeUpdate.java:232)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.requestUpdate(TypeUpdate.java:212)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.updateTypeForSsaVar(TypeUpdate.java:183)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.updateTypeChecked(TypeUpdate.java:112)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.apply(TypeUpdate.java:83)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.apply(TypeUpdate.java:56)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.calculateFromBounds(FixTypesVisitor.java:156)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.setBestType(FixTypesVisitor.java:133)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.deduceType(FixTypesVisitor.java:238)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.tryDeduceTypes(FixTypesVisitor.java:221)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.visit(FixTypesVisitor.java:91)
     */
    /* JADX WARN: Failed to calculate best type for var: r9v2 ??
    java.lang.NullPointerException: Cannot invoke "jadx.core.dex.instructions.args.InsnArg.getType()" because "changeArg" is null
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.moveListener(TypeUpdate.java:439)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.runListeners(TypeUpdate.java:232)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.requestUpdate(TypeUpdate.java:212)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.updateTypeForSsaVar(TypeUpdate.java:183)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.updateTypeChecked(TypeUpdate.java:112)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.apply(TypeUpdate.java:83)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.apply(TypeUpdate.java:56)
    	at jadx.core.dex.visitors.typeinference.TypeInferenceVisitor.calculateFromBounds(TypeInferenceVisitor.java:145)
    	at jadx.core.dex.visitors.typeinference.TypeInferenceVisitor.setBestType(TypeInferenceVisitor.java:123)
    	at jadx.core.dex.visitors.typeinference.TypeInferenceVisitor.lambda$runTypePropagation$2(TypeInferenceVisitor.java:101)
    	at java.base/java.util.ArrayList.forEach(ArrayList.java:1604)
    	at jadx.core.dex.visitors.typeinference.TypeInferenceVisitor.runTypePropagation(TypeInferenceVisitor.java:101)
    	at jadx.core.dex.visitors.typeinference.TypeInferenceVisitor.visit(TypeInferenceVisitor.java:75)
     */
    /* JADX WARN: Multi-variable type inference failed. Error: java.lang.NullPointerException: Cannot invoke "jadx.core.dex.instructions.args.RegisterArg.getSVar()" because the return value of "jadx.core.dex.nodes.InsnNode.getResult()" is null
    	at jadx.core.dex.visitors.typeinference.AbstractTypeConstraint.collectRelatedVars(AbstractTypeConstraint.java:31)
    	at jadx.core.dex.visitors.typeinference.AbstractTypeConstraint.<init>(AbstractTypeConstraint.java:19)
    	at jadx.core.dex.visitors.typeinference.TypeSearch$1.<init>(TypeSearch.java:376)
    	at jadx.core.dex.visitors.typeinference.TypeSearch.makeMoveConstraint(TypeSearch.java:376)
    	at jadx.core.dex.visitors.typeinference.TypeSearch.makeConstraint(TypeSearch.java:361)
    	at jadx.core.dex.visitors.typeinference.TypeSearch.collectConstraints(TypeSearch.java:341)
    	at java.base/java.util.ArrayList.forEach(ArrayList.java:1604)
    	at jadx.core.dex.visitors.typeinference.TypeSearch.run(TypeSearch.java:60)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.runMultiVariableSearch(FixTypesVisitor.java:116)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.visit(FixTypesVisitor.java:91)
     */
    /* JADX WARN: Not initialized variable reg: 10, insn: 0x00e9: MOVE (r0 I:??[int, float, boolean, short, byte, char, OBJECT, ARRAY]) = (r10 I:??[int, float, boolean, short, byte, char, OBJECT, ARRAY]), block:B:47:0x00e9 */
    /* JADX WARN: Not initialized variable reg: 11, insn: 0x0089: MOVE (r0 I:??[int, float, boolean, short, byte, char, OBJECT, ARRAY]) = (r11 I:??[int, float, boolean, short, byte, char, OBJECT, ARRAY] A[D('rs' java.sql.ResultSet)]) A[TRY_LEAVE], block:B:23:0x0089 */
    /* JADX WARN: Not initialized variable reg: 12, insn: 0x008e: MOVE (r0 I:??[int, float, boolean, short, byte, char, OBJECT, ARRAY]) = (r12 I:??[int, float, boolean, short, byte, char, OBJECT, ARRAY]), block:B:25:0x008e */
    /* JADX WARN: Not initialized variable reg: 9, insn: 0x00e5: MOVE (r0 I:??[int, float, boolean, short, byte, char, OBJECT, ARRAY]) = (r9 I:??[int, float, boolean, short, byte, char, OBJECT, ARRAY] A[D('s' java.sql.Statement)]) A[TRY_LEAVE], block:B:45:0x00e5 */
    /* JADX WARN: Type inference failed for: r10v0, types: [java.lang.Throwable] */
    /* JADX WARN: Type inference failed for: r11v0, names: [rs], types: [java.sql.ResultSet] */
    /* JADX WARN: Type inference failed for: r12v0, types: [java.lang.Throwable] */
    /* JADX WARN: Type inference failed for: r9v2, names: [s], types: [java.sql.Statement] */
    public long getMaxLogicalLobSize() throws SQLException {
        ?? r9;
        ?? r10;
        ?? r11;
        ?? r12;
        Monitor.CloseableLock lock = this.connection.acquireCloseableLock();
        Throwable th = null;
        try {
            if (this.maxLogicalLobSize == -1) {
                try {
                    try {
                        Statement statementCreateStatement = this.connection.createStatement();
                        Throwable th2 = null;
                        try {
                            ResultSet resultSetExecuteQuery = statementCreateStatement.executeQuery("select value from v$parameter where name = 'db_block_size'");
                            Throwable th3 = null;
                            if (resultSetExecuteQuery.next()) {
                                this.maxLogicalLobSize = 4294967295L * resultSetExecuteQuery.getLong(1);
                            } else {
                                this.maxLogicalLobSize = 0L;
                            }
                            if (resultSetExecuteQuery != null) {
                                if (0 != 0) {
                                    try {
                                        resultSetExecuteQuery.close();
                                    } catch (Throwable th4) {
                                        th3.addSuppressed(th4);
                                    }
                                } else {
                                    resultSetExecuteQuery.close();
                                }
                            }
                            if (statementCreateStatement != null) {
                                if (0 != 0) {
                                    try {
                                        statementCreateStatement.close();
                                    } catch (Throwable th5) {
                                        th2.addSuppressed(th5);
                                    }
                                } else {
                                    statementCreateStatement.close();
                                }
                            }
                        } catch (Throwable th6) {
                            if (r11 != 0) {
                                if (r12 != 0) {
                                    try {
                                        r11.close();
                                    } catch (Throwable th7) {
                                        r12.addSuppressed(th7);
                                    }
                                } else {
                                    r11.close();
                                }
                            }
                            throw th6;
                        }
                    } catch (Throwable th8) {
                        if (r9 != 0) {
                            if (r10 != 0) {
                                try {
                                    r9.close();
                                } catch (Throwable th9) {
                                    r10.addSuppressed(th9);
                                }
                            } else {
                                r9.close();
                            }
                        }
                        throw th8;
                    }
                } catch (SQLException e) {
                    this.maxLogicalLobSize = 0L;
                    if (e.getErrorCode() == 942) {
                        throw ((SQLException) DatabaseError.createSqlException(DatabaseError.EOJ_REQUIRE_SELECT).fillInStackTrace());
                    }
                    throw e;
                }
            }
            long j = this.maxLogicalLobSize;
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th10) {
                        th.addSuppressed(th10);
                    }
                } else {
                    lock.close();
                }
            }
            return j;
        } catch (Throwable th11) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th12) {
                        th.addSuppressed(th12);
                    }
                } else {
                    lock.close();
                }
            }
            throw th11;
        }
    }

    public boolean supportsRefCursors() throws SQLException {
        return true;
    }

    @Override // oracle.jdbc.AdditionalDatabaseMetaData
    public Map<String, String> getAnnotations(String objectName, String columnName, String domainName, String domainOwner) throws SQLException {
        if (objectName == null || objectName.trim().length() == 0) {
            throw new SQLException("Invalid ObjectName : " + objectName);
        }
        if (columnName == null || columnName.trim().length() == 0) {
            throw new SQLException("Invalid ColumnName : " + columnName);
        }
        return getAnnotationsInternal(objectName, columnName, domainName, domainOwner);
    }

    @Override // oracle.jdbc.AdditionalDatabaseMetaData
    public Map<String, String> getAnnotations(String objectName, String domainName, String domainOwner) throws SQLException {
        if (objectName == null || objectName.trim().length() == 0) {
            throw new SQLException("Invalid ObjectName : " + objectName);
        }
        return getAnnotationsInternal(objectName, null, domainName, domainOwner);
    }

    private Map<String, String> getAnnotationsInternal(String objectName, String columnName, String domainName, String domainOwner) throws SQLException {
        String sql = "SELECT ANNOTATION_NAME, ANNOTATION_VALUE FROM USER_ANNOTATIONS_USAGE WHERE OBJECT_NAME = ?" + (columnName == null ? " AND COLUMN_NAME IS NULL" : "AND COLUMN_NAME = ?");
        String sql2 = (sql + (domainName == null ? " AND DOMAIN_NAME IS NULL" : "AND DOMAIN_NAME = ?")) + (domainOwner == null ? " AND DOMAIN_OWNER IS NULL" : "AND DOMAIN_OWNER = ?");
        Map<String, String> annotations = new LinkedHashMap<>();
        Monitor.CloseableLock lock = this.connection.acquireCloseableLock();
        Throwable th = null;
        try {
            PreparedStatement pstmt = this.connection.prepareStatement(sql2);
            Throwable th2 = null;
            try {
                try {
                    int indx = 1 + 1;
                    pstmt.setString(1, objectName.toUpperCase());
                    if (columnName != null) {
                        indx++;
                        pstmt.setString(indx, columnName.toUpperCase());
                    }
                    if (domainName != null) {
                        int i = indx;
                        indx++;
                        pstmt.setString(i, domainName.toUpperCase());
                    }
                    if (domainOwner != null) {
                        int i2 = indx;
                        int i3 = indx + 1;
                        pstmt.setString(i2, domainOwner.toUpperCase());
                    }
                    ResultSet rs = pstmt.executeQuery();
                    while (rs.next()) {
                        String name = rs.getString(1);
                        String value = rs.getString(2);
                        annotations.put(name, value == null ? "" : value);
                    }
                    rs.close();
                    if (pstmt != null) {
                        if (0 != 0) {
                            try {
                                pstmt.close();
                            } catch (Throwable th3) {
                                th2.addSuppressed(th3);
                            }
                        } else {
                            pstmt.close();
                        }
                    }
                    return annotations;
                } finally {
                }
            } catch (Throwable th4) {
                if (pstmt != null) {
                    if (th2 != null) {
                        try {
                            pstmt.close();
                        } catch (Throwable th5) {
                            th2.addSuppressed(th5);
                        }
                    } else {
                        pstmt.close();
                    }
                }
                throw th4;
            }
        } finally {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th6) {
                        th.addSuppressed(th6);
                    }
                } else {
                    lock.close();
                }
            }
        }
    }
}
